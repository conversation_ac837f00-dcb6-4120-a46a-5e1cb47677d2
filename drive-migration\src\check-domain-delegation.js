import { googleAuth } from './auth/google-auth.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Check if domain-wide delegation is properly configured
 */
async function checkDomainWideDelegation() {
    console.log('🔧 Checking Domain-Wide Delegation Configuration');
    console.log('='.repeat(60));

    const userEmail = process.env.GOOGLE_ADMIN_EMAIL;

    if (!userEmail) {
        console.log('❌ GOOGLE_ADMIN_EMAIL not set in .env file');
        process.exit(1);
    }

    console.log(`👤 Testing domain-wide delegation for: ${userEmail}`);
    console.log(`🔑 Service account: ${googleAuth.serviceAccountEmail}`);
    console.log(`📁 Project: ${googleAuth.projectId}`);

    try {
        // Test connection
        const testResult = await googleAuth.testConnection(userEmail);

        console.log('\n📋 Test Results:');
        console.log(`   Connected as: ${testResult.connectedAs}`);
        console.log(`   Domain-wide delegation working: ${testResult.domainWideDelegationWorking ? '✅ YES' : '❌ NO'}`);
        console.log(`   Permissions: ${testResult.permissions.join(', ')}`);

        if (testResult.errors.length > 0) {
            console.log('\n❌ Errors found:');
            testResult.errors.forEach((error, i) => {
                console.log(`   ${i + 1}. ${error}`);
            });
        }

        if (testResult.domainWideDelegationWorking) {
            console.log('\n🎉 SUCCESS: Domain-wide delegation is working correctly!');
            console.log('You can now proceed with Google Drive scanning and migration.');

            // Test file listing
            console.log('\n📁 Testing file listing...');
            const { googleDriveAPI } = await import('./api/google-drive-api.js');

            const files = await googleDriveAPI.listFiles(userEmail, {
                pageSize: 5,
                q: 'trashed=false'
            });

            console.log(`✅ Found ${files.files.length} files in user's Drive`);
            if (files.files.length > 0) {
                console.log('📋 Sample files:');
                files.files.slice(0, 3).forEach(file => {
                    console.log(`   - ${file.name} (${file.mimeType})`);
                });
            } else {
                console.log('ℹ️ User has no files in Drive (this is normal for new accounts)');
            }

        } else {
            console.log('\n❌ FAILED: Domain-wide delegation is NOT working');
            console.log('\n💡 To fix this issue:');
            console.log('1. Follow the setup guide in DOMAIN_WIDE_DELEGATION_SETUP.md');
            console.log('2. Ensure the service account is authorized in Google Admin Console');
            console.log('3. Wait 5-10 minutes after making changes');
            console.log('4. Run this script again to verify');

            console.log(`\n🔑 Service Account Details Needed:`);
            console.log(`   Client ID: ${googleAuth.clientId}`);
            console.log(`   Required Scopes:`);
            console.log(`   - https://www.googleapis.com/auth/drive`);
            console.log(`   - https://www.googleapis.com/auth/drive.file`);
            console.log(`   - https://www.googleapis.com/auth/drive.metadata`);
            console.log(`   - https://www.googleapis.com/auth/drive.readonly`);
        }

    } catch (error) {
        console.error('\n❌ Domain-wide delegation check failed:', error.message);

        if (error.message.includes('unauthorized_client')) {
            console.log('\n💡 This means the service account is not authorized for domain-wide delegation.');
            console.log('Please follow the setup guide in DOMAIN_WIDE_DELEGATION_SETUP.md');
        }
    }
}

// Auto-check every 30 seconds if in watch mode
if (process.argv.includes('--watch')) {
    console.log('👀 Watch mode enabled. Checking every 30 seconds...');
    setInterval(checkDomainWideDelegation, 30000);
}

checkDomainWideDelegation();
