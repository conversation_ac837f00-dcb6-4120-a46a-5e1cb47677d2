# Google Drive API Domain-Wide Delegation Setup Guide

## Issue Identified
The Google service account is successfully authenticating but **not properly impersonating users**. It's accessing its own Drive (which has no files) instead of the user's Drive.

## Root Cause
Domain-wide delegation is not properly configured in Google Admin Console.

## Solution Steps

### Step 1: Enable Domain-Wide Delegation in Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **IAM & Admin** → **Service Accounts**
3. Find your service account: `<EMAIL>`
4. Click on the service account
5. Go to the **Details** tab
6. Scroll down to **Advanced settings**
7. Check **"Enable Google Workspace Domain-wide Delegation"**
8. Note down the **Client ID** (should be: `114472688725345184870`)

### Step 2: Authorize Service Account in Google Admin Console

1. Go to [Google Admin Console](https://admin.google.com/)
2. Navigate to **Security** → **Access and data control** → **API Controls**
3. Click **"Manage Domain Wide Delegation"**
4. Click **"Add new"**
5. Enter the **Client ID**: `114472688725345184870`
6. Add these **OAuth Scopes** (comma-separated):
   ```
   https://www.googleapis.com/auth/drive,
   https://www.googleapis.com/auth/drive.file,
   https://www.googleapis.com/auth/drive.metadata,
   https://www.googleapis.com/auth/drive.readonly
   ```
7. Click **"Authorize"**

### Step 3: Verify User Domain

Ensure that the user email `<EMAIL>` belongs to the same Google Workspace organization where the service account is authorized.

### Step 4: Test Configuration

After completing the above steps, wait 5-10 minutes for changes to propagate, then run:

```bash
node src/simple-drive-test-fixed.js
```

Expected result after fix:
- ✅ Connected as: <EMAIL> (not the service account email)
- Files should be listed if the user has any

## Current Configuration

### Service Account Details
- **Email**: <EMAIL>
- **Client ID**: 114472688725345184870
- **Project ID**: osp-drive-migration-465913

### Required Scopes
- `https://www.googleapis.com/auth/drive`
- `https://www.googleapis.com/auth/drive.file`
- `https://www.googleapis.com/auth/drive.metadata`
- `https://www.googleapis.com/auth/drive.readonly`

## Troubleshooting

### If domain-wide delegation still doesn't work:

1. **Check Organization**: Verify that `<EMAIL>` is in the same Google Workspace organization
2. **Wait for Propagation**: Domain-wide delegation changes can take up to 24 hours to fully propagate
3. **Check Admin Rights**: Ensure you have super admin rights in Google Admin Console
4. **Verify Client ID**: Double-check that the Client ID matches exactly
5. **Check Scopes**: Ensure all required scopes are added

### Alternative Test Users

If the current user has no files, try with a user who definitely has files in their Drive, or create some test files first.

## Next Steps

1. Complete the domain-wide delegation setup above
2. Test with the diagnostic script
3. Once working, the Drive scanner should successfully list files and folders from the user's account
