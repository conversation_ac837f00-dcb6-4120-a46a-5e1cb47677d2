import { googleAuth } from './auth/google-auth.js';
import { googleDriveAPI } from './api/google-drive-api.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Comprehensive Google Drive troubleshooting script
 */
async function troubleshootGoogleDrive() {
    console.log('🔧 Google Drive Troubleshooting Tool');
    console.log('='.repeat(60));

    const userEmail = process.env.GOOGLE_ADMIN_EMAIL || process.env.GOOGLE_TEST_EMAIL;

    if (!userEmail) {
        console.log('❌ No user email provided. Set GOOGLE_ADMIN_EMAIL or GOOGLE_TEST_EMAIL in .env');
        process.exit(1);
    }

    try {
        // Step 1: Validate credentials
        console.log('\n📋 Step 1: Validating credentials...');
        const isValid = googleAuth.validateCredentials();
        if (!isValid) {
            console.log('❌ Invalid credentials. Cannot proceed.');
            return;
        }
        console.log('✅ Credentials are valid');

        // Step 2: Run comprehensive troubleshooting
        console.log('\n📋 Step 2: Running troubleshooting diagnostics...');
        const diagnostics = await googleAuth.troubleshootAccess(userEmail);

        if (!diagnostics.canProceed) {
            console.log('\n❌ Cannot proceed with Drive operations. Fix the issues above first.');
            return;
        }

        // Step 3: Test basic Drive operations
        console.log('\n📋 Step 3: Testing basic Drive operations...');
        try {
            const testResult = await googleAuth.testConnection(userEmail);
            if (testResult.success) {
                console.log(`✅ Connected as: ${testResult.connectedAs}`);
                console.log(`✅ Available permissions: ${testResult.permissions.join(', ')}`);
            } else {
                console.log('❌ Connection test failed');
                testResult.errors.forEach(error => console.log(`   ${error}`));
                return;
            }
        } catch (connError) {
            console.log(`❌ Connection test error: ${connError.message}`);
            return;
        }

        // Step 4: Test file listing with different strategies
        console.log('\n📋 Step 4: Testing file listing strategies...');

        // Strategy 1: Basic file listing
        console.log('\n   Strategy 1: Basic file listing');
        try {
            const basicFiles = await googleDriveAPI.listFiles(userEmail, {
                pageSize: 10,
                q: 'trashed=false'
            });
            console.log(`   ✅ Basic listing: ${basicFiles.files.length} files found`);

            if (basicFiles.files.length > 0) {
                console.log('   📁 Sample files:');
                basicFiles.files.slice(0, 3).forEach(file => {
                    console.log(`      - ${file.name} (${file.mimeType})`);
                });
            }
        } catch (basicError) {
            console.log(`   ❌ Basic listing failed: ${basicError.message}`);
        }

        // Strategy 2: Include all drives
        console.log('\n   Strategy 2: Include all drives and shared drives');
        try {
            const allDrivesFiles = await googleDriveAPI.listFiles(userEmail, {
                pageSize: 10,
                q: 'trashed=false',
                supportsAllDrives: true,
                includeItemsFromAllDrives: true,
                corpora: 'allDrives'
            });
            console.log(`   ✅ All drives listing: ${allDrivesFiles.files.length} files found`);
        } catch (allDrivesError) {
            console.log(`   ❌ All drives listing failed: ${allDrivesError.message}`);
        }

        // Strategy 3: Root folder only
        console.log('\n   Strategy 3: Root folder only');
        try {
            const rootFiles = await googleDriveAPI.listFiles(userEmail, {
                pageSize: 10,
                q: "'root' in parents and trashed=false"
            });
            console.log(`   ✅ Root folder listing: ${rootFiles.files.length} files found`);
        } catch (rootError) {
            console.log(`   ❌ Root folder listing failed: ${rootError.message}`);
        }

        // Strategy 4: Include trashed files
        console.log('\n   Strategy 4: Include trashed files');
        try {
            const allFiles = await googleDriveAPI.listFiles(userEmail, {
                pageSize: 10,
                q: '' // No filter
            });
            console.log(`   ✅ All files (including trashed): ${allFiles.files.length} files found`);
        } catch (allError) {
            console.log(`   ❌ All files listing failed: ${allError.message}`);
        }

        // Step 5: Test drive scanning service
        console.log('\n📋 Step 5: Testing drive scanning service...');
        try {
            const { driveScanner } = await import('./services/drive-scanner.js');

            // Test scanner status
            const scanStatus = driveScanner.getScanStatus();
            console.log(`   📊 Scanner status: ${scanStatus.isScanning ? 'Running' : 'Idle'}`);

            // Test a limited scan
            console.log(`   🔍 Testing limited scan (depth: 1)...`);
            const scanOptions = {
                maxDepth: 1,
                pageSize: 5
            };

            const scanResult = await driveScanner.startFullScan(userEmail, scanOptions);
            console.log(`   ✅ Scan completed: ${scanResult.totalFiles} files, ${scanResult.totalSize} bytes`);

        } catch (scanError) {
            console.log(`   ❌ Drive scanner test failed: ${scanError.message}`);
        }

        // Step 6: Summary and recommendations
        console.log('\n📋 Step 6: Summary and recommendations');
        console.log('✅ Troubleshooting completed successfully!');
        console.log('\n💡 Recommendations:');
        console.log('1. If no files are found, check if the user has any files in their Drive');
        console.log('2. Ensure domain-wide delegation is properly configured');
        console.log('3. Verify that the user belongs to the organization');
        console.log('4. Check if files are in shared drives that require special permissions');

    } catch (error) {
        console.error('\n❌ Troubleshooting failed:', error.message);
        console.error(error.stack);
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    troubleshootGoogleDrive();
}

export { troubleshootGoogleDrive };
