#!/usr/bin/env node

/**
 * Test Error Handling Script
 * Script để test tất cả các t<PERSON>h năng xử lý lỗi
 */

// Simple API utilities for testing
const apiCall = async (url, options = {}) => {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });

  if (!response.ok) {
    let errorData;
    try {
      errorData = await response.json();
    } catch (e) {
      errorData = { message: response.statusText };
    }

    const error = new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    error.status = response.status;
    error.statusText = response.statusText;
    error.response = {
      status: response.status,
      statusText: response.statusText,
      data: errorData
    };

    throw error;
  }

  try {
    return await response.json();
  } catch (e) {
    return await response.text();
  }
};

const apiGet = (url, options = {}) => apiCall(url, { method: 'GET', ...options });
const apiPost = (url, data = null, options = {}) => apiCall(url, {
  method: 'POST',
  body: data ? JSON.stringify(data) : null,
  ...options
});

const formatError = (error) => {
  if (typeof error === 'string') {
    return { message: error, details: null, code: null };
  }

  if (error instanceof Error) {
    return {
      message: error.message,
      details: error.stack,
      code: error.status || error.code || null
    };
  }

  return {
    message: error.message || 'Unknown error',
    details: JSON.stringify(error, null, 2),
    code: null
  };
};

const BASE_URL = 'http://localhost:3000';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  test: (msg) => console.log(`${colors.cyan}🧪 ${msg}${colors.reset}`)
};

// Test cases
const testCases = [
  {
    name: 'Test 400 Bad Request',
    url: '/api/test-error/400',
    method: 'POST',
    expectedStatus: 400
  },
  {
    name: 'Test 401 Unauthorized',
    url: '/api/test-error/401',
    method: 'GET',
    expectedStatus: 401
  },
  {
    name: 'Test 404 Not Found',
    url: '/api/test-error/404',
    method: 'GET',
    expectedStatus: 404
  },
  {
    name: 'Test 500 Internal Server Error',
    url: '/api/test-error/500',
    method: 'POST',
    expectedStatus: 500
  },
  {
    name: 'Test 503 Service Unavailable',
    url: '/api/test-error/503',
    method: 'GET',
    expectedStatus: 503
  },
  {
    name: 'Test Validation Error',
    url: '/api/test-error/validation',
    method: 'POST',
    data: { email: 'invalid', password: '123' },
    expectedStatus: 400
  },
  {
    name: 'Test Success Response',
    url: '/api/test-error/success',
    method: 'GET',
    expectedStatus: 200,
    shouldSucceed: true
  },
  {
    name: 'Test Network Error',
    url: 'http://localhost:9999/api/test',
    method: 'GET',
    expectNetworkError: true
  }
];

async function runTest(testCase) {
  log.test(`Running: ${testCase.name}`);

  try {
    let result;
    const fullUrl = testCase.url.startsWith('http') ? testCase.url : `${BASE_URL}${testCase.url}`;

    if (testCase.method === 'GET') {
      result = await apiGet(fullUrl);
    } else {
      result = await apiPost(fullUrl, testCase.data);
    }

    if (testCase.shouldSucceed) {
      log.success(`✓ ${testCase.name} - Success as expected`);
      console.log(`  Response:`, JSON.stringify(result, null, 2));
      return { success: true, result };
    } else {
      log.error(`✗ ${testCase.name} - Expected error but got success`);
      return { success: false, error: 'Expected error but got success' };
    }

  } catch (error) {
    if (testCase.shouldSucceed) {
      log.error(`✗ ${testCase.name} - Unexpected error`);
      console.log(`  Error:`, error.message);
      return { success: false, error };
    }

    // Expected error
    const errorInfo = formatError(error);

    if (testCase.expectNetworkError) {
      if (error.message.includes('Lỗi kết nối')) {
        log.success(`✓ ${testCase.name} - Network error as expected`);
        return { success: true, error };
      } else {
        log.error(`✗ ${testCase.name} - Expected network error but got: ${error.message}`);
        return { success: false, error };
      }
    }

    if (testCase.expectedStatus && error.status === testCase.expectedStatus) {
      log.success(`✓ ${testCase.name} - Error status ${error.status} as expected`);
      console.log(`  Message: ${errorInfo.message}`);
      if (errorInfo.details) {
        console.log(`  Details: ${errorInfo.details.substring(0, 100)}...`);
      }
      return { success: true, error };
    } else {
      log.error(`✗ ${testCase.name} - Expected status ${testCase.expectedStatus} but got ${error.status}`);
      console.log(`  Error:`, errorInfo.message);
      return { success: false, error };
    }
  }
}

async function testErrorFormatting() {
  log.test('Testing Error Formatting');

  // Test string error
  const stringError = formatError('Simple string error');
  console.log('String error:', stringError);

  // Test Error object
  const errorObj = new Error('Test error message');
  errorObj.status = 500;
  const formattedError = formatError(errorObj);
  console.log('Error object:', formattedError);

  // Test API error
  const apiError = new Error('API Error');
  apiError.response = {
    status: 400,
    statusText: 'Bad Request',
    data: { message: 'Invalid data', details: 'Field validation failed' }
  };
  const formattedApiError = formatError(apiError);
  console.log('API error:', formattedApiError);

  log.success('Error formatting tests completed');
}

async function checkServerHealth() {
  log.info('Checking server health...');

  try {
    const health = await apiGet(`${BASE_URL}/api/health`);
    log.success('Server is healthy');
    console.log('Health check:', health);
    return true;
  } catch (error) {
    log.error('Server health check failed');
    console.log('Error:', error.message);
    return false;
  }
}

async function main() {
  console.log(`${colors.magenta}🚀 Error Handling Test Suite${colors.reset}\n`);

  // Check server health first
  const serverHealthy = await checkServerHealth();
  if (!serverHealthy) {
    log.error('Server is not running. Please start the server first.');
    process.exit(1);
  }

  console.log('');

  // Test error formatting
  await testErrorFormatting();
  console.log('');

  // Run all test cases
  let passed = 0;
  let failed = 0;

  for (const testCase of testCases) {
    const result = await runTest(testCase);
    if (result.success) {
      passed++;
    } else {
      failed++;
    }
    console.log(''); // Empty line between tests
  }

  // Summary
  console.log(`${colors.magenta}📊 Test Summary${colors.reset}`);
  log.success(`Passed: ${passed}`);
  if (failed > 0) {
    log.error(`Failed: ${failed}`);
  } else {
    log.success(`Failed: ${failed}`);
  }

  console.log(`\n${colors.cyan}💡 Tips:${colors.reset}`);
  console.log('- Open http://localhost:5174 to see the frontend error handling');
  console.log('- Use the Error Test Panel in development mode to test UI errors');
  console.log('- Check the browser console for detailed error information');

  if (failed > 0) {
    process.exit(1);
  }
}

// Run the tests
main().catch(error => {
  log.error('Test suite failed');
  console.error(error);
  process.exit(1);
});
