import dotenv from 'dotenv';

dotenv.config();

console.log('🔍 Testing Environment Configuration...\n');

// Test Google credentials
console.log('=== GOOGLE CONFIGURATION ===');
console.log('GOOGLE_SERVICE_ACCOUNT_EMAIL:', process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL ? '✅ Set' : '❌ Missing');
console.log('GOOGLE_PRIVATE_KEY:', process.env.GOOGLE_PRIVATE_KEY ? '✅ Set' : '❌ Missing');
console.log('GOOGLE_PROJECT_ID:', process.env.GOOGLE_PROJECT_ID ? '✅ Set' : '❌ Missing');
console.log('GOOGLE_CLIENT_EMAIL:', process.env.GOOGLE_CLIENT_EMAIL ? '✅ Set' : '❌ Missing');
console.log('GOOGLE_CLIENT_ID:', process.env.GOOGLE_CLIENT_ID ? '✅ Set' : '❌ Missing');
console.log('GOOGLE_ADMIN_EMAIL:', process.env.GOOGLE_ADMIN_EMAIL ? '✅ Set' : '❌ Missing');

// Test Lark credentials
console.log('\n=== LARK CONFIGURATION ===');
console.log('LARK_APP_ID:', process.env.LARK_APP_ID ? '✅ Set' : '❌ Missing');
console.log('LARK_APP_SECRET:', process.env.LARK_APP_SECRET ? '✅ Set' : '❌ Missing');

// Test Supabase credentials
console.log('\n=== SUPABASE CONFIGURATION ===');
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? '✅ Set' : '❌ Missing');
console.log('SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Set' : '❌ Missing');

// Test other config
console.log('\n=== APPLICATION CONFIGURATION ===');
console.log('NODE_ENV:', process.env.NODE_ENV || 'development');
console.log('PORT:', process.env.PORT || '3000');
console.log('LOG_LEVEL:', process.env.LOG_LEVEL || 'info');

console.log('\n✅ Environment configuration check completed!');

// Test basic imports
console.log('\n🔍 Testing Basic Imports...\n');

try {
    console.log('Testing googleapis import...');
    const { google } = await import('googleapis');
    console.log('✅ googleapis imported successfully');
} catch (error) {
    console.log('❌ googleapis import failed:', error.message);
}

try {
    console.log('Testing @supabase/supabase-js import...');
    const { createClient } = await import('@supabase/supabase-js');
    console.log('✅ @supabase/supabase-js imported successfully');
} catch (error) {
    console.log('❌ @supabase/supabase-js import failed:', error.message);
}

try {
    console.log('Testing jsonwebtoken import...');
    const jwt = await import('jsonwebtoken');
    console.log('✅ jsonwebtoken imported successfully');
} catch (error) {
    console.log('❌ jsonwebtoken import failed:', error.message);
}

console.log('\n✅ Basic imports test completed!');
