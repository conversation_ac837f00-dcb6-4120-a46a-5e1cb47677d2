import { google } from 'googleapis';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Google Service Account Authentication với Domain-wide Delegation
 * Cho phép ứng dụng giả lập người dùng để truy cập Drive
 * Enhanced version với caching và error handling
 */
export class GoogleAuth {
    constructor() {
        this.serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
        this.privateKey = process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n');
        this.projectId = process.env.GOOGLE_PROJECT_ID;
        this.clientEmail = process.env.GOOGLE_CLIENT_EMAIL;
        this.clientId = process.env.GOOGLE_CLIENT_ID;

        // Cache cho auth clients
        this.authClientCache = new Map();
        this.tokenCache = new Map();

        // Scopes mặc định cho migration
        this.defaultScopes = [
            'https://www.googleapis.com/auth/drive',
            'https://www.googleapis.com/auth/drive.file',
            'https://www.googleapis.com/auth/drive.metadata',
            'https://www.googleapis.com/auth/admin.directory.user.readonly'
        ];

        this.keyPath = join(__dirname, '..', '..', 'google-service-account.json');

        if (!this.serviceAccountEmail || !this.privateKey || !this.projectId) {
            throw new Error('Missing required Google Service Account credentials (GOOGLE_SERVICE_ACCOUNT_EMAIL, GOOGLE_PRIVATE_KEY, GOOGLE_PROJECT_ID)');
        }

        console.log('✅ Google Auth initialized with Service Account:', this.serviceAccountEmail);
    }

    /**
     * Tạo JWT token để giả lập người dùng
     * @param {string} userEmail - Email của người dùng cần giả lập
     * @param {string[]} scopes - Các scope cần thiết
     * @returns {string} JWT token
     */
    createJWT(userEmail, scopes = null) {
        const now = Math.floor(Date.now() / 1000);
        const scopesToUse = scopes || this.defaultScopes;

        const payload = {
            iss: this.serviceAccountEmail,
            sub: userEmail, // Giả lập người dùng này
            scope: scopesToUse.join(' '),
            aud: 'https://oauth2.googleapis.com/token',
            iat: now,
            exp: now + 3600 // Token có hiệu lực 1 giờ
        };

        return jwt.sign(payload, this.privateKey, { algorithm: 'RS256' });
    }

    /**
     * Validate Service Account credentials
     * @returns {boolean} True nếu credentials hợp lệ
     */
    validateCredentials() {
        try {
            // Test tạo JWT với dummy email
            const testJWT = this.createJWT('<EMAIL>');
            return !!testJWT;
        } catch (error) {
            console.error('❌ Invalid Service Account credentials:', error.message);
            return false;
        }
    }

    /**
     * Lấy access token từ Google OAuth2 với caching
     * @param {string} userEmail - Email người dùng cần giả lập
     * @param {boolean} forceRefresh - Bắt buộc refresh token
     * @returns {Promise<string>} Access token
     */
    async getAccessToken(userEmail, forceRefresh = false) {
        const cacheKey = `token_${userEmail}`;

        // Check cache nếu không force refresh
        if (!forceRefresh && this.tokenCache.has(cacheKey)) {
            const cached = this.tokenCache.get(cacheKey);
            if (cached.expires > Date.now()) {
                return cached.token;
            }
        }

        try {
            const auth = new google.auth.GoogleAuth({
                keyFile: this.keyPath,
                scopes: this.defaultScopes,
                subject: userEmail // Domain-wide delegation
            });

            const authClient = await auth.getClient();
            const accessTokenResponse = await authClient.getAccessToken();

            // Cache token với expiry time
            this.tokenCache.set(cacheKey, {
                token: accessTokenResponse.token,
                expires: Date.now() + (50 * 60 * 1000) // 50 phút (token có hiệu lực 1h)
            });

            return accessTokenResponse.token;
        } catch (error) {
            console.error('❌ Error getting access token:', error);
            throw new Error(`Failed to get access token for ${userEmail}: ${error.message}`);
        }
    }

    /**
     * Tạo authenticated Google Drive client với caching
     * @param {string} userEmail - Email người dùng cần giả lập
     * @param {string[]} scopes - Custom scopes (optional)
     * @returns {Promise<object>} Google Drive API client
     */
    async getDriveClient(userEmail, scopes = null) {
        const cacheKey = `client_${userEmail}`;

        // Check cache
        if (this.authClientCache.has(cacheKey)) {
            const cached = this.authClientCache.get(cacheKey);
            if (cached.expires > Date.now()) {
                return cached.client;
            }
        }

        try {
            const scopesToUse = scopes || this.defaultScopes;

            const auth = new google.auth.GoogleAuth({
                keyFile: this.keyPath,
                scopes: scopesToUse,
                subject: userEmail
            });

            const authClient = await auth.getClient();
            const driveClient = google.drive({ version: 'v3', auth: authClient });

            // Cache client
            this.authClientCache.set(cacheKey, {
                client: driveClient,
                expires: Date.now() + (50 * 60 * 1000) // 50 phút
            });

            return driveClient;
        } catch (error) {
            console.error('❌ Error creating Drive client:', error);
            throw new Error(`Failed to create Drive client for ${userEmail}: ${error.message}`);
        }
    }

    /**
     * Tạo authenticated Admin Directory client
     * @param {string} userEmail - Email admin cần giả lập
     * @returns {Promise<object>} Admin Directory API client
     */
    async getAdminClient(userEmail) {
        try {
            const auth = new google.auth.GoogleAuth({
                keyFile: this.keyPath,
                scopes: ['https://www.googleapis.com/auth/admin.directory.user.readonly'],
                subject: userEmail
            });

            const authClient = await auth.getClient();
            return google.admin({ version: 'directory_v1', auth: authClient });
        } catch (error) {
            console.error('❌ Error creating Admin client:', error);
            throw new Error(`Failed to create Admin client for ${userEmail}: ${error.message}`);
        }
    }

    /**
     * Kiểm tra kết nối và quyền truy cập
     * @param {string} userEmail - Email người dùng để test
     * @returns {Promise<object>} Test result với details
     */
    async testConnection(userEmail) {
        const result = {
            success: false,
            userEmail: userEmail,
            connectedAs: null,
            permissions: [],
            errors: [],
            domainWideDelegationWorking: false
        };

        try {
            // Test Drive API connection
            console.log(`🔍 Testing Google Drive connection for: ${userEmail}`);

            const drive = await this.getDriveClient(userEmail);
            const aboutResponse = await drive.about.get({
                fields: 'user,storageQuota,canCreateDrives'
            });

            result.connectedAs = aboutResponse.data.user.emailAddress;
            result.storageQuota = aboutResponse.data.storageQuota;
            result.canCreateDrives = aboutResponse.data.canCreateDrives;

            // Check if domain-wide delegation is working correctly
            if (result.connectedAs === userEmail) {
                result.domainWideDelegationWorking = true;
                console.log(`✅ Domain-wide delegation working: Successfully impersonating ${userEmail}`);
            } else {
                result.domainWideDelegationWorking = false;
                console.log(`❌ Domain-wide delegation NOT working: Connected as ${result.connectedAs} instead of ${userEmail}`);
                result.errors.push(`Domain-wide delegation failed: Expected ${userEmail}, got ${result.connectedAs}`);
            }

            // Test basic Drive operations
            const filesResponse = await drive.files.list({
                pageSize: 1,
                fields: 'files(id,name)'
            });

            result.permissions.push('drive.files.list');

            // Test permissions API
            try {
                const testFile = filesResponse.data.files?.[0];
                if (testFile) {
                    await drive.permissions.list({
                        fileId: testFile.id,
                        fields: 'permissions(id,type,role,emailAddress)'
                    });
                    result.permissions.push('drive.permissions.list');
                }
            } catch (permError) {
                result.errors.push(`Permissions API: ${permError.message}`);
            }

            result.success = result.domainWideDelegationWorking;
            console.log(`✅ Available permissions: ${result.permissions.join(', ')}`);

        } catch (error) {
            result.errors.push(`Drive API: ${error.message}`);
            console.error('❌ Drive connection test failed:', error.message);

            // Provide specific guidance
            if (error.message.includes('unauthorized_client')) {
                result.errors.push('Domain-wide delegation not authorized in Google Admin Console');
            } else if (error.message.includes('invalid_grant')) {
                result.errors.push(`User ${userEmail} not found or not in the organization`);
            }
        }

        // Test Admin Directory API (optional)
        try {
            const admin = await this.getAdminClient(userEmail);
            await admin.users.get({ userKey: userEmail });
            result.permissions.push('admin.directory.users.readonly');
            console.log('✅ Admin Directory API accessible');
        } catch (adminError) {
            result.errors.push(`Admin API: ${adminError.message}`);
            console.log('⚠️ Admin Directory API not accessible (optional)');
        }

        return result;
    }

    /**
     * Clear all caches
     */
    clearCache() {
        this.authClientCache.clear();
        this.tokenCache.clear();
        console.log('🧹 Auth cache cleared');
    }

    /**
     * Get cache statistics
     */
    getCacheStats() {
        return {
            authClients: this.authClientCache.size,
            tokens: this.tokenCache.size,
            authClientKeys: Array.from(this.authClientCache.keys()),
            tokenKeys: Array.from(this.tokenCache.keys())
        };
    }

    /**
     * Validate Domain-wide Delegation setup
     * @param {string} adminEmail - Email của admin để test
     * @returns {Promise<object>} Validation result
     */
    async validateDomainWideDelegation(adminEmail) {
        const result = {
            success: false,
            serviceAccount: this.serviceAccountEmail,
            adminEmail: adminEmail,
            delegationWorking: false,
            errors: []
        };

        try {
            // Test với admin email
            const adminTest = await this.testConnection(adminEmail);

            if (adminTest.success) {
                result.delegationWorking = true;
                result.success = true;
                console.log('✅ Domain-wide delegation is working correctly');
            } else {
                result.errors = adminTest.errors;
                console.log('❌ Domain-wide delegation test failed');
            }

        } catch (error) {
            result.errors.push(`Delegation test: ${error.message}`);
            console.error('❌ Domain-wide delegation validation failed:', error.message);
        }

        return result;
    }

    /**
     * Troubleshoot Google Drive API access issues
     * @param {string} userEmail - Email to test
     * @returns {Promise<object>} Diagnostic results
     */
    async troubleshootAccess(userEmail) {
        console.log(`🔍 Troubleshooting Google Drive access for: ${userEmail}`);
        console.log('='.repeat(60));

        const diagnostics = {
            userEmail,
            serviceAccount: this.serviceAccountEmail,
            projectId: this.projectId,
            issues: [],
            suggestions: [],
            canProceed: false
        };

        // 1. Check service account credentials
        console.log('1. Checking service account credentials...');
        try {
            if (!this.serviceAccountEmail || !this.privateKey || !this.projectId) {
                diagnostics.issues.push('Missing service account credentials');
                diagnostics.suggestions.push('Ensure GOOGLE_SERVICE_ACCOUNT_EMAIL, GOOGLE_PRIVATE_KEY, and GOOGLE_PROJECT_ID are set');
            } else {
                console.log('   ✅ Service account credentials are present');
            }
        } catch (error) {
            diagnostics.issues.push(`Credential validation error: ${error.message}`);
        }

        // 2. Test JWT creation
        console.log('2. Testing JWT creation...');
        try {
            const testJWT = this.createJWT(userEmail);
            if (testJWT) {
                console.log('   ✅ JWT creation successful');
            }
        } catch (error) {
            diagnostics.issues.push(`JWT creation failed: ${error.message}`);
            diagnostics.suggestions.push('Check private key format and ensure it\'s properly escaped');
        }

        // 3. Test basic authentication
        console.log('3. Testing basic authentication...');
        try {
            const auth = new google.auth.GoogleAuth({
                keyFile: this.keyPath,
                scopes: this.defaultScopes
            });
            const authClient = await auth.getClient();
            console.log('   ✅ Basic authentication successful');
        } catch (error) {
            diagnostics.issues.push(`Basic auth failed: ${error.message}`);
            diagnostics.suggestions.push('Verify service account key file is valid and accessible');
        }

        // 4. Test domain-wide delegation
        console.log('4. Testing domain-wide delegation...');
        try {
            const auth = new google.auth.GoogleAuth({
                keyFile: this.keyPath,
                scopes: this.defaultScopes,
                subject: userEmail
            });
            const authClient = await auth.getClient();
            const drive = google.drive({ version: 'v3', auth: authClient });

            // Try to get user info
            const about = await drive.about.get({ fields: 'user' });
            if (about.data.user && about.data.user.emailAddress === userEmail) {
                console.log('   ✅ Domain-wide delegation working');
                diagnostics.canProceed = true;
            } else {
                diagnostics.issues.push('Domain-wide delegation not properly configured');
                diagnostics.suggestions.push('Enable domain-wide delegation in Google Cloud Console and authorize the service account in Google Admin Console');
            }
        } catch (error) {
            diagnostics.issues.push(`Domain-wide delegation failed: ${error.message}`);

            if (error.message.includes('unauthorized_client')) {
                diagnostics.suggestions.push('Service account needs domain-wide delegation authorization in Google Admin Console');
                diagnostics.suggestions.push('Add these scopes in Admin Console: https://www.googleapis.com/auth/drive, https://www.googleapis.com/auth/drive.file');
            } else if (error.message.includes('insufficient permission')) {
                diagnostics.suggestions.push('User does not have sufficient permissions or is not in the organization');
            } else if (error.message.includes('invalid_grant')) {
                diagnostics.suggestions.push('Check if the user email is valid and belongs to the organization');
            }
        }

        // 5. Test file listing (if delegation works)
        if (diagnostics.canProceed) {
            console.log('5. Testing file listing...');
            try {
                const drive = await this.getDriveClient(userEmail);
                const response = await drive.files.list({
                    pageSize: 1,
                    fields: 'files(id,name)'
                });
                console.log(`   ✅ File listing successful (found ${response.data.files?.length || 0} files)`);
            } catch (error) {
                diagnostics.issues.push(`File listing failed: ${error.message}`);
                diagnostics.suggestions.push('Check if user has any files or if additional scopes are needed');
            }
        }

        // Summary
        console.log('\n📋 Diagnostic Summary:');
        console.log(`   Issues found: ${diagnostics.issues.length}`);
        console.log(`   Can proceed: ${diagnostics.canProceed ? '✅ Yes' : '❌ No'}`);

        if (diagnostics.issues.length > 0) {
            console.log('\n❌ Issues:');
            diagnostics.issues.forEach((issue, i) => {
                console.log(`   ${i + 1}. ${issue}`);
            });
        }

        if (diagnostics.suggestions.length > 0) {
            console.log('\n💡 Suggestions:');
            diagnostics.suggestions.forEach((suggestion, i) => {
                console.log(`   ${i + 1}. ${suggestion}`);
            });
        }

        return diagnostics;
    }
}

// Export singleton instance
export const googleAuth = new GoogleAuth();